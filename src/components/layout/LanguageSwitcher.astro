---
// 获取当前语言
const { currentLocale = 'en' } = Astro.props;

// 支持的语言列表
const languages = [
  { code: 'en', name: 'English' },
  { code: 'ar', name: 'العربية' },
  { code: 'pt', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' }
];

// 获取当前语言对象
const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];
---

<div class="language-switcher">
  <!-- 桌面版语言切换器 -->
  <div class="th-desktop-view relative z-[100]">
    <button type="button" id="desktop-language-button" class="flex items-center space-x-1 text-text-secondary hover:text-white px-3 py-2 rounded-full hover:bg-dark-hover transition-colors">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
      </svg>
      <span id="desktop-current-language">English</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>
    
    <div id="desktop-language-dropdown" class="absolute left-0 mt-2 w-40 bg-black rounded-lg shadow-xl overflow-hidden z-[100] hidden">
      <div class="py-1">
        {languages.map(lang => (
          <button
            type="button"
            data-lang-code={lang.code}
            data-lang-url={lang.code === 'en' ? '/' : `/${lang.code}/`}
            class="block w-full text-left px-4 py-2 text-white hover:bg-dark-hover transition-colors desktop-language-option"
          >
            {lang.name}
          </button>
        ))}
      </div>
    </div>
  </div>
  
  <!-- 移动版语言切换器 - 使用下拉选择 -->
  <div class="th-mobile-view relative z-[100]">
    <button type="button" id="mobile-language-button" class="flex items-center space-x-1 text-text-secondary hover:text-white px-3 py-2 rounded-full hover:bg-dark-hover transition-colors">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
      </svg>
      <span id="mobile-current-language" class="text-sm">English</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <div id="mobile-language-dropdown" class="absolute right-0 mt-2 w-36 bg-black rounded-lg shadow-xl overflow-hidden z-[100] hidden">
      <div class="py-1">
        {languages.map(lang => (
          <button
            type="button"
            data-lang-code={lang.code}
            data-lang-url={lang.code === 'en' ? '/' : `/${lang.code}/`}
            class="block w-full text-left px-4 py-2 text-white hover:bg-dark-hover transition-colors mobile-language-option text-sm"
          >
            {lang.name}
          </button>
        ))}
      </div>
    </div>
  </div>
</div>

<style>
  /* 确保下拉菜单在桌面设备上可见 */
  #desktop-language-dropdown {
    background-color: #000000 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    border: 1px solid #333;
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
  }

  #desktop-language-dropdown.show {
    display: block !important;
  }

  /* 移动端下拉菜单样式 */
  #mobile-language-dropdown {
    background-color: #000000 !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    border: 1px solid #333;
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
  }

  #mobile-language-dropdown.show {
    display: block !important;
  }

  .desktop-language-option,
  .mobile-language-option {
    cursor: pointer;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
  }

  .desktop-language-option:hover,
  .mobile-language-option:hover {
    background-color: #333 !important;
  }

  /* 移除移动设备上的点击高亮 */
  #desktop-language-button,
  #mobile-language-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* 调试样式 - 确保移动端按钮可见 */
  @media (max-width: 1024px) {
    #mobile-language-button {
      border: 1px solid red !important; /* 临时调试边框 */
    }

    #desktop-language-button {
      display: none !important;
    }
  }

  @media (min-width: 1025px) {
    #mobile-language-button {
      display: none !important;
    }
  }
</style>

<script>
  // 语言切换器初始化
  function initLanguageSwitcher() {
    console.log('Initializing language switcher...');

    // 检查当前设备类型
    const isDesktop = window.matchMedia('(min-width: 1024px)').matches;
    console.log('Current device type:', isDesktop ? 'desktop' : 'mobile');
    console.log('Window width:', window.innerWidth);

    // 设置当前语言 - 优先使用URL路径中的语言代码
    const pathname = window.location.pathname;
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];
    const supportedLanguages = ['en', 'ar', 'pt'];
    const hasLocalePrefix = supportedLanguages.includes(firstSegment);

    let currentLang = 'en';
    if (hasLocalePrefix) {
      currentLang = firstSegment;
    } else {
      // 如果URL中没有语言代码，则尝试从cookie或localStorage获取
      const cookieMatch = document.cookie.match(/preferred_language=([a-z]{2})/i);
      if (cookieMatch && cookieMatch[1]) {
        currentLang = cookieMatch[1];
      } else {
        currentLang = localStorage.getItem('language') || 'en';
      }
    }

    // 语言名称映射
    const languageNames = {
      'en': 'English',
      'ar': 'العربية',
      'pt': 'Português'
    };

    // 构建正确的语言URL
    function buildLanguageUrl(langCode) {
      const currentPath = window.location.pathname;
      const currentSearch = window.location.search;
      const currentHash = window.location.hash;

      // 移除当前语言前缀
      let cleanPath = currentPath;
      const supportedLangs = ['en', 'ar', 'pt'];
      const pathSegments = currentPath.split('/').filter(Boolean);

      if (pathSegments.length > 0 && supportedLangs.includes(pathSegments[0])) {
        cleanPath = '/' + pathSegments.slice(1).join('/');
      }

      // 构建新URL
      let newUrl;
      if (langCode === 'en') {
        newUrl = cleanPath || '/';
      } else {
        newUrl = `/${langCode}${cleanPath}`;
      }

      return newUrl + currentSearch + currentHash;
    }

    // 保存语言偏好并导航
    function switchLanguage(langCode) {
      localStorage.setItem('language', langCode);
      document.cookie = `preferred_language=${langCode}; path=/; max-age=${60 * 60 * 24 * 365}; SameSite=Lax`;

      // 设置文档方向
      if (langCode === 'ar') {
        document.documentElement.dir = 'rtl';
      } else {
        document.documentElement.dir = 'ltr';
      }

      // 导航到新URL
      const newUrl = buildLanguageUrl(langCode);
      window.location.href = newUrl;
    }

    // 桌面版语言切换器
    const desktopButton = document.getElementById('desktop-language-button');
    const desktopDropdown = document.getElementById('desktop-language-dropdown');
    const desktopCurrentText = document.getElementById('desktop-current-language');

    if (desktopButton && desktopDropdown && desktopCurrentText) {
      console.log('Desktop elements found');
      // 更新显示的语言
      desktopCurrentText.textContent = languageNames[currentLang] || 'English';

      // 清除之前的事件监听器
      const newDesktopButton = desktopButton.cloneNode(true);
      desktopButton.parentNode.replaceChild(newDesktopButton, desktopButton);

      // 桌面版按钮点击事件
      newDesktopButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Desktop language button clicked');
        const isVisible = desktopDropdown.classList.contains('show');
        if (isVisible) {
          desktopDropdown.classList.remove('show');
          console.log('Desktop dropdown hidden');
        } else {
          desktopDropdown.classList.add('show');
          console.log('Desktop dropdown shown');
        }
      });

      // 点击其他地方关闭下拉菜单
      document.addEventListener('click', function(e) {
        if (desktopDropdown.classList.contains('show') &&
            !newDesktopButton.contains(e.target) &&
            !desktopDropdown.contains(e.target)) {
          desktopDropdown.classList.remove('show');
        }
      });

      // 为桌面版语言选项添加事件
      document.querySelectorAll('.desktop-language-option').forEach(function(option) {
        option.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          const langCode = this.getAttribute('data-lang-code');
          desktopDropdown.classList.remove('show');
          switchLanguage(langCode);
        });
      });
    }

    // 移动版语言切换器 - 使用下拉菜单
    const mobileButton = document.getElementById('mobile-language-button');
    const mobileDropdown = document.getElementById('mobile-language-dropdown');
    const mobileCurrentText = document.getElementById('mobile-current-language');

    if (mobileButton && mobileDropdown && mobileCurrentText) {
      console.log('Mobile elements found');
      // 更新显示的语言
      mobileCurrentText.textContent = languageNames[currentLang] || 'English';

      // 清除之前的事件监听器
      const newMobileButton = mobileButton.cloneNode(true);
      mobileButton.parentNode.replaceChild(newMobileButton, mobileButton);

      // 移动版按钮点击事件
      newMobileButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Mobile language button clicked');
        const isVisible = mobileDropdown.classList.contains('show');
        if (isVisible) {
          mobileDropdown.classList.remove('show');
          console.log('Mobile dropdown hidden');
        } else {
          mobileDropdown.classList.add('show');
          console.log('Mobile dropdown shown');
        }
      });

      // 点击其他地方关闭下拉菜单
      document.addEventListener('click', function(e) {
        if (mobileDropdown.classList.contains('show') &&
            !newMobileButton.contains(e.target) &&
            !mobileDropdown.contains(e.target)) {
          mobileDropdown.classList.remove('show');
        }
      });

      // 为移动版语言选项添加事件
      document.querySelectorAll('.mobile-language-option').forEach(function(option) {
        option.addEventListener('click', function(e) {
          e.preventDefault();
          e.stopPropagation();
          const langCode = this.getAttribute('data-lang-code');
          mobileDropdown.classList.remove('show');
          switchLanguage(langCode);
        });
      });
    }

    // 设置文档方向
    if (currentLang === 'ar') {
      document.documentElement.dir = 'rtl';
    } else {
      document.documentElement.dir = 'ltr';
    }
  }

  // 立即初始化
  document.addEventListener('DOMContentLoaded', initLanguageSwitcher);

  // 监听设备类型变化事件
  document.addEventListener('device-type-initialized', initLanguageSwitcher);
  document.addEventListener('device-type-changed', initLanguageSwitcher);
</script>
