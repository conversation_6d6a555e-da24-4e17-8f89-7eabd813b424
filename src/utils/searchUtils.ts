// 搜索工具函数
import fs from 'fs/promises';
import path from 'path';

// 搜索结果接口
export interface SearchResult {
  id: string;
  title: string;
  artist: string;
  category: string;
  era?: string;
  url: string;
  type: 'track' | 'era';
  length?: string;
  notes?: string;
}

// 艺术家信息接口
export interface ArtistInfo {
  id: string;
  name: string;
  displayName: string;
}

// 获取所有艺术家信息
export async function getAllArtists(): Promise<ArtistInfo[]> {
  try {
    const artistsPath = path.join(process.cwd(), 'src', 'data', 'artists.json');
    const artistsData = await fs.readFile(artistsPath, 'utf-8');
    const data = JSON.parse(artistsData);

    // 检查数据结构
    if (data.artists && Array.isArray(data.artists)) {
      return data.artists.map((artist: any) => ({
        id: artist.id,
        name: artist.name,
        displayName: artist.name
      }));
    }

    // 如果是直接的数组
    if (Array.isArray(data)) {
      return data.map((artist: any) => ({
        id: artist.id,
        name: artist.name,
        displayName: artist.displayName || artist.name
      }));
    }

    console.error('艺术家数据格式不正确:', data);
    return [];
  } catch (error) {
    console.error('获取艺术家数据失败:', error);
    return [];
  }
}

// 获取艺术家的所有分类
export async function getArtistCategories(artistId: string): Promise<string[]> {
  try {
    const categoriesPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories');
    const categories = await fs.readdir(categoriesPath);
    return categories.filter(cat => !cat.includes('.'));
  } catch (error) {
    console.error(`获取艺术家 ${artistId} 的分类失败:`, error);
    return [];
  }
}

// 搜索曲目数据
export async function searchTracks(artistId: string, categoryId: string, query: string): Promise<SearchResult[]> {
  const results: SearchResult[] = [];
  const queryLower = query.toLowerCase();

  try {
    const tracksPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'tracks.json');

    // 检查文件是否存在
    try {
      await fs.access(tracksPath);
    } catch {
      // 文件不存在，返回空结果
      return results;
    }

    const tracksData = await fs.readFile(tracksPath, 'utf-8');
    const tracks = JSON.parse(tracksData);
    
    // 获取艺术家信息
    const artists = await getAllArtists();
    const artist = artists.find(a => a.id === artistId);
    const artistName = artist?.displayName || artistId;
    
    // 遍历所有时代的曲目
    for (const era of tracks) {
      if (era.tracks && Array.isArray(era.tracks)) {
        for (const track of era.tracks) {
          // 检查曲目标题、艺术家、别名等是否匹配
          const titleMatch = track.title?.toLowerCase().includes(queryLower);
          const artistMatch = track.artists?.toLowerCase().includes(queryLower);
          const aliasMatch = track.aliases?.some((alias: string) => alias.toLowerCase().includes(queryLower));
          const notesMatch = track.notes?.toLowerCase().includes(queryLower);
          
          if (titleMatch || artistMatch || aliasMatch || notesMatch) {
            results.push({
              id: track.id || track.key,
              title: track.title,
              artist: artistName,
              category: categoryId,
              era: era.era,
              url: `/artists/${artistId}/${categoryId}#${track.id || track.key}`,
              type: 'track',
              length: track.length,
              notes: track.notes
            });
          }
        }
      }
    }
  } catch (error) {
    // 静默处理错误，避免日志污染
    // console.error(`搜索 ${artistId}/${categoryId} 曲目失败:`, error);
  }
  
  return results;
}

// 搜索专辑数据 (仅适用于 unreleased 分类)
export async function searchEras(artistId: string, categoryId: string, query: string): Promise<SearchResult[]> {
  const results: SearchResult[] = [];
  const queryLower = query.toLowerCase();

  // 只有 unreleased 分类才有 eras 结构
  if (categoryId !== 'unreleased') {
    return results;
  }

  try {
    // 检查是否有 eras.json 文件
    const erasIndexPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');

    try {
      await fs.access(erasIndexPath);
    } catch {
      // 如果没有 eras.json 文件，返回空结果
      return results;
    }
    
    const erasData = await fs.readFile(erasIndexPath, 'utf-8');
    const eras = JSON.parse(erasData);
    
    // 获取艺术家信息
    const artists = await getAllArtists();
    const artist = artists.find(a => a.id === artistId);
    const artistName = artist?.displayName || artistId;
    
    // 搜索专辑名称
    for (const era of eras) {
      if (era.name?.toLowerCase().includes(queryLower)) {
        results.push({
          id: era.id,
          title: era.name,
          artist: artistName,
          category: categoryId,
          url: `/artists/${artistId}/${categoryId}/eras/${era.id}`,
          type: 'era'
        });
      }
      
      // 搜索专辑内的曲目
      try {
        const eraDetailPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras', `${era.id}.json`);
        const eraDetailData = await fs.readFile(eraDetailPath, 'utf-8');
        const eraDetail = JSON.parse(eraDetailData);
        
        if (eraDetail.tracks && Array.isArray(eraDetail.tracks)) {
          for (const track of eraDetail.tracks) {
            const titleMatch = track.name?.toLowerCase().includes(queryLower);
            const artistMatch = track.artists?.some((artist: string) => artist.toLowerCase().includes(queryLower));
            const notesMatch = track.notes?.toLowerCase().includes(queryLower);
            
            if (titleMatch || artistMatch || notesMatch) {
              results.push({
                id: track.id,
                title: track.name,
                artist: artistName,
                category: categoryId,
                era: era.name,
                url: `/artists/${artistId}/${categoryId}/eras/${era.id}#${track.id}`,
                type: 'track',
                length: track.length,
                notes: track.notes
              });
            }
          }
        }
      } catch (error) {
        // 如果专辑详情文件不存在，继续处理下一个专辑
        // console.warn(`专辑详情文件不存在: ${era.id}`);
      }
    }
  } catch (error) {
    // 静默处理错误
    // console.error(`搜索 ${artistId}/${categoryId} 专辑失败:`, error);
  }
  
  return results;
}

// 全局搜索函数
export async function globalSearch(query: string, limit: number = 20): Promise<SearchResult[]> {
  if (!query.trim()) {
    return [];
  }

  const allResults: SearchResult[] = [];
  const artists = await getAllArtists();

  // 遍历所有艺术家
  for (const artist of artists) {
    const categories = await getArtistCategories(artist.id);

    // 遍历每个艺术家的所有分类
    for (const category of categories) {
      if (category === 'unreleased') {
        // unreleased 分类使用 eras 结构
        const eraResults = await searchEras(artist.id, category, query);
        allResults.push(...eraResults);
      } else {
        // 其他分类使用 tracks.json 结构
        const trackResults = await searchTracks(artist.id, category, query);
        allResults.push(...trackResults);
      }
    }
  }
  
  // 按相关性排序（简单的字符串匹配度排序）
  const queryLower = query.toLowerCase();
  allResults.sort((a, b) => {
    const aScore = calculateRelevanceScore(a, queryLower);
    const bScore = calculateRelevanceScore(b, queryLower);
    return bScore - aScore;
  });
  
  // 返回限制数量的结果
  return allResults.slice(0, limit);
}

// Calculate search relevance score with improved fuzzy matching
function calculateRelevanceScore(result: SearchResult, query: string): number {
  const queryLower = query.toLowerCase().trim();
  const title = result.title.toLowerCase();
  const artist = result.artist.toLowerCase();
  const notes = (result.notes || '').toLowerCase();
  const era = (result.era || '').toLowerCase();

  let score = 0;

  // Title matching (highest priority)
  if (title === queryLower) score += 100;
  else if (title.startsWith(queryLower)) score += 85;
  else if (title.includes(queryLower)) score += 70;
  else if (containsFuzzyMatch(title, queryLower)) score += 50;

  // Artist matching
  if (artist === queryLower) score += 90;
  else if (artist.includes(queryLower)) score += 60;
  else if (containsFuzzyMatch(artist, queryLower)) score += 40;

  // Era/Album matching
  if (era === queryLower) score += 80;
  else if (era.includes(queryLower)) score += 50;
  else if (containsFuzzyMatch(era, queryLower)) score += 30;

  // Notes matching (lowest priority)
  if (notes.includes(queryLower)) score += 20;
  else if (containsFuzzyMatch(notes, queryLower)) score += 10;

  return score;
}

// Fuzzy matching helper function for improved search
function containsFuzzyMatch(text: string, query: string): boolean {
  if (!text || !query) return false;

  // Remove special characters and normalize spaces
  const cleanText = text.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();
  const cleanQuery = query.replace(/[^\w\s]/g, ' ').replace(/\s+/g, ' ').trim();

  // Split query into words for partial matching
  const queryWords = cleanQuery.split(' ').filter(word => word.length > 0);

  // Check if all query words are found in the text (case insensitive)
  return queryWords.every(word =>
    cleanText.includes(word) ||
    cleanText.split(' ').some(textWord =>
      textWord.includes(word) ||
      word.includes(textWord) ||
      // Check for partial word matches (minimum 3 characters)
      (word.length >= 3 && textWord.length >= 3 &&
       (textWord.startsWith(word.substring(0, 3)) || word.startsWith(textWord.substring(0, 3))))
    )
  );
}
