// 搜索 API 端点
import type { APIRoute } from 'astro';
import { globalSearch } from '../../utils/searchUtils';

// 禁用预渲染，确保在服务器端运行
export const prerender = false;

export const GET: APIRoute = async ({ url, request }) => {
  try {
    // 从 URL 中提取查询参数 - 使用多种方法确保兼容性
    let query: string | null = null;
    let limitParam: string | null = null;

    // 方法1: 使用 Astro 的 url 对象
    query = url.searchParams.get('q');
    limitParam = url.searchParams.get('limit');

    // 方法2: 如果方法1失败，尝试从 request.url 解析
    if (!query && request.url) {
      try {
        const urlObj = new URL(request.url);
        query = urlObj.searchParams.get('q');
        limitParam = urlObj.searchParams.get('limit');
      } catch (e) {
        // 忽略解析错误
      }
    }

    // 方法3: 正则表达式备用解析
    if (!query && request.url) {
      const qMatch = request.url.match(/[?&]q=([^&]*)/);
      const limitMatch = request.url.match(/[?&]limit=([^&]*)/);

      if (qMatch) {
        query = decodeURIComponent(qMatch[1]);
      }
      if (limitMatch) {
        limitParam = decodeURIComponent(limitMatch[1]);
      }
    }

    // 简单日志记录
    console.log('Search API called with query:', query);



    // Validate query parameter
    if (!query || query.trim().length === 0) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Search query cannot be empty',
        results: []
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
    
    // Validate query length
    if (query.length > 100) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Search query too long',
        results: []
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }

    // Parse limit parameter
    const limit = limitParam ? Math.min(parseInt(limitParam, 10), 50) : 20;

    // Execute search
    const results = await globalSearch(query.trim(), limit);

    // Return search results
    return new Response(JSON.stringify({
      success: true,
      query: query.trim(),
      count: results.length,
      results: results
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=300', // 缓存5分钟
      },
    });
    
  } catch (error) {
    console.error('搜索 API 错误:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: '搜索服务暂时不可用',
      results: []
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
};



// 预检请求处理
export const OPTIONS: APIRoute = async () => {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
};
